from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import date, timedelta, datetime
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.mail import send_mail
from django.conf import settings
import json

from .models import (
    Employee, Department, Designation, LeaveType,
    HolidayCalendar, LeaveBalance, LeaveRequest, Attendance
)
from .forms import DepartmentForm, LeaveTypeForm, DesignationForm

def check_leave_balance_availability(leave_request):
    """Check if employee has sufficient leave balance"""
    try:
        current_year = date.today().year
        leave_balance = LeaveBalance.objects.filter(
            employee=leave_request.employee,
            leave_type=leave_request.leave_type,
            year=current_year
        ).first()

        if not leave_balance:
            # Create default balance if doesn't exist
            leave_balance = LeaveBalance.objects.create(
                employee=leave_request.employee,
                leave_type=leave_request.leave_type,
                year=current_year,
                total_allocated=leave_request.leave_type.max_days_per_year,
                used_days=0,
                carry_forward=0
            )

        available_days = leave_balance.available_days
        shortage = max(0, leave_request.total_days - available_days)
        return {
            'has_sufficient_balance': available_days >= leave_request.total_days,
            'available_days': available_days,
            'requested_days': leave_request.total_days,
            'balance_after_approval': available_days - leave_request.total_days,
            'shortage': shortage
        }
    except Exception as e:
        print(f"Error checking leave balance: {str(e)}")
        return {
            'has_sufficient_balance': False,
            'available_days': 0,
            'requested_days': leave_request.total_days,
            'balance_after_approval': 0,
            'shortage': leave_request.total_days
        }

def update_leave_balance(leave_request):
    """Update employee leave balance when a leave request is approved"""
    try:
        # Get or create leave balance for the employee and leave type for current year
        current_year = date.today().year
        leave_balance, created = LeaveBalance.objects.get_or_create(
            employee=leave_request.employee,
            leave_type=leave_request.leave_type,
            year=current_year,
            defaults={
                'total_allocated': leave_request.leave_type.max_days_per_year,
                'used_days': 0,
                'carry_forward': 0
            }
        )

        # Update used days
        leave_balance.used_days += leave_request.total_days
        leave_balance.save()

        return True
    except Exception as e:
        print(f"Error updating leave balance: {str(e)}")
        return False

def send_leave_notification(leave_request):
    """Send email notification to employee about leave request status"""
    try:
        employee_email = leave_request.employee.user.email
        if not employee_email:
            return False

        status = leave_request.status
        subject = f"Leave Request {status} - {leave_request.leave_type.name}"

        if status == 'Approved':
            message = f"""
Dear {leave_request.employee.full_name},

Your leave request has been APPROVED.

Leave Details:
- Leave Type: {leave_request.leave_type.name}
- Start Date: {leave_request.start_date.strftime('%B %d, %Y')}
- End Date: {leave_request.end_date.strftime('%B %d, %Y')}
- Total Days: {leave_request.total_days}
- Approved By: {leave_request.approved_by.full_name}
- Approved On: {leave_request.approved_on.strftime('%B %d, %Y at %I:%M %p')}

Enjoy your leave!

Best regards,
HR Team
Smart Management System
            """
        else:  # Rejected
            message = f"""
Dear {leave_request.employee.full_name},

Your leave request has been REJECTED.

Leave Details:
- Leave Type: {leave_request.leave_type.name}
- Start Date: {leave_request.start_date.strftime('%B %d, %Y')}
- End Date: {leave_request.end_date.strftime('%B %d, %Y')}
- Total Days: {leave_request.total_days}
- Rejected By: {leave_request.approved_by.full_name}
- Rejected On: {leave_request.approved_on.strftime('%B %d, %Y at %I:%M %p')}

Reason for Rejection:
{leave_request.rejection_reason or 'No specific reason provided.'}

Please contact HR if you have any questions.

Best regards,
HR Team
Smart Management System
            """

        send_mail(
            subject=subject,
            message=message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[employee_email],
            fail_silently=True,
        )
        return True

    except Exception as e:
        print(f"Error sending email notification: {str(e)}")
        return False

# Dashboard Views
@login_required
def leave_attendance_dashboard(request):
    """Main dashboard for Leave & Attendance Management"""
    try:
        employee = request.user.employee_profile
    except:
        # If user doesn't have employee profile, redirect to create one
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('core:create_employee_profile')

    # Get current year data
    current_year = date.today().year
    current_month = date.today().month

    # Leave Statistics
    leave_balances = LeaveBalance.objects.filter(employee=employee, year=current_year)
    total_leave_balance = sum([lb.remaining_days for lb in leave_balances])

    # Recent leave requests
    recent_leaves = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')[:5]

    # Attendance Statistics for current month
    attendance_records = Attendance.objects.filter(
        employee=employee,
        date__year=current_year,
        date__month=current_month
    )

    present_days = attendance_records.filter(status='Present').count()
    absent_days = attendance_records.filter(status='Absent').count()
    leave_days = attendance_records.filter(status='Leave').count()
    wfh_days = attendance_records.filter(status='WFH').count()

    # Working hours this month
    total_working_hours = attendance_records.aggregate(
        total=Sum('working_hours')
    )['total'] or 0

    # Upcoming holidays
    upcoming_holidays = HolidayCalendar.objects.filter(
        date__gte=date.today()
    ).order_by('date')[:5]

    context = {
        'employee': employee,
        'total_leave_balance': total_leave_balance,
        'leave_balances': leave_balances,
        'recent_leaves': recent_leaves,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'wfh_days': wfh_days,
        'total_working_hours': total_working_hours,
        'upcoming_holidays': upcoming_holidays,
        'current_month': date.today().strftime('%B %Y'),
    }

    return render(request, 'core/dashboard.html', context)

@login_required
def admin_leave_dashboard(request):
    """Admin dashboard for Leave & Attendance Management"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('leave_attendance_dashboard')

    # Get statistics
    total_employees = Employee.objects.filter(status='Active').count()
    pending_leaves = LeaveRequest.objects.filter(status='Pending').count()

    # Today's attendance
    today = date.today()
    today_attendance = Attendance.objects.filter(date=today)
    present_today = today_attendance.filter(status='Present').count()
    absent_today = today_attendance.filter(status='Absent').count()

    # Recent leave requests
    recent_leave_requests = LeaveRequest.objects.select_related(
        'employee__user', 'leave_type'
    ).order_by('-applied_on')[:10]

    # Department wise statistics
    departments = Department.objects.annotate(
        employee_count=Count('employee', filter=Q(employee__status='Active'))
    )

    context = {
        'total_employees': total_employees,
        'pending_leaves': pending_leaves,
        'present_today': present_today,
        'absent_today': absent_today,
        'recent_leave_requests': recent_leave_requests,
        'departments': departments,
    }

    return render(request, 'core/admin_dashboard.html', context)

# Employee Profile Management
@login_required
def create_employee_profile(request):
    """Create employee profile for users who don't have one"""
    try:
        # Check if user already has an employee profile
        employee = request.user.employee_profile
        messages.info(request, 'You already have an employee profile.')
        return redirect('core:dashboard')
    except:
        pass

    if request.method == 'POST':
        from .forms import EmployeeForm
        form = EmployeeForm(request.POST)
        if form.is_valid():
            employee = form.save(commit=False)
            employee.user = request.user
            employee.save()
            messages.success(request, 'Employee profile created successfully!')
            return redirect('core:dashboard')
    else:
        from .forms import EmployeeForm
        form = EmployeeForm()

    return render(request, 'core/employee_profile_form.html', {'form': form})

@login_required
def edit_employee_profile(request):
    """Edit current user's employee profile"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('core:create_employee_profile')

    if request.method == 'POST':
        from .forms import EmployeeForm
        form = EmployeeForm(request.POST, instance=employee)
        if form.is_valid():
            try:
                employee = form.save()
                messages.success(request, 'Your profile has been updated successfully!')
                return redirect('core:dashboard')
            except Exception as e:
                messages.error(request, f'Error updating profile: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        from .forms import EmployeeForm
        form = EmployeeForm(instance=employee)

    context = {
        'form': form,
        'employee': employee,
        'page_title': 'Edit Profile',
        'page_description': 'Update your employee information',
        'form_action': 'Update',
        'is_edit': True
    }
    return render(request, 'core/employee_profile_form.html', context)

# API Views for AJAX calls
@login_required
def check_leave_balance(request):
    """API endpoint to check leave balances"""
    try:
        employee = request.user.employee_profile
        current_year = date.today().year
        balances = LeaveBalance.objects.filter(employee=employee, year=current_year)

        balance_data = []
        for balance in balances:
            balance_data.append({
                'leave_type': balance.leave_type.name,
                'total_allocated': balance.total_allocated,
                'used_days': float(balance.used_days),
                'remaining_days': balance.remaining_days
            })

        return JsonResponse({'balances': balance_data})
    except:
        return JsonResponse({'balances': []})

@login_required
@require_POST
def calculate_leave_days(request):
    """API endpoint to calculate leave days"""
    try:
        data = json.loads(request.body)
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        is_half_day = data.get('is_half_day', False)

        if is_half_day:
            total_days = 0.5
        else:
            # Simple calculation - can be enhanced to exclude weekends/holidays
            total_days = (end_date - start_date).days + 1

            # Exclude weekends
            current_date = start_date
            weekend_days = 0
            while current_date <= end_date:
                if current_date.weekday() >= 5:  # Saturday=5, Sunday=6
                    weekend_days += 1
                current_date += timedelta(days=1)

            total_days -= weekend_days

            # Exclude holidays
            holidays = HolidayCalendar.objects.filter(
                date__range=[start_date, end_date]
            ).count()
            total_days -= holidays

            total_days = max(0, total_days)

        return JsonResponse({'total_days': total_days})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

# Leave Request Views
@login_required
def leave_request_create(request):
    """Create a new leave request"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('core:create_employee_profile')

    if request.method == 'POST':
        from .forms import LeaveRequestForm
        form = LeaveRequestForm(request.POST, employee=employee)
        if form.is_valid():
            leave_request = form.save(commit=False)
            leave_request.employee = employee
            leave_request.save()
            messages.success(request, 'Leave request submitted successfully!')
            return redirect('core:leave_request_list')
    else:
        from .forms import LeaveRequestForm
        form = LeaveRequestForm(employee=employee)

    return render(request, 'core/leave_request_form.html', {'form': form})

@login_required
def leave_request_list(request):
    """List all leave requests for the current user or all if admin"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.user.role == 'admin':
        leave_requests = LeaveRequest.objects.select_related('employee__user', 'leave_type').order_by('-applied_on')
    else:
        leave_requests = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')

    # Pagination
    paginator = Paginator(leave_requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'is_admin': request.user.role == 'admin'
    }

    return render(request, 'core/leave_request_list.html', context)

# Placeholder views for other functionalities
@login_required
def employee_list(request):
    return render(request, 'core/employee_list.html')

@login_required
def employee_create(request):
    context = {'page_title': 'Add Employee', 'page_description': 'Create new employee profile'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_detail(request, pk):
    context = {'page_title': 'Employee Details', 'page_description': 'View employee information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_edit(request, pk):
    context = {'page_title': 'Edit Employee', 'page_description': 'Update employee information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_delete(request, pk):
    return redirect('core:employee_list')

@login_required
def department_list(request):
    """List all departments with search and pagination"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    # Get search query
    search_query = request.GET.get('search', '')

    # Base queryset with employee count annotation
    departments = Department.objects.annotate(
        employee_count=Count('employee', filter=Q(employee__status='Active'))
    ).order_by('name')

    # Apply search filter
    if search_query:
        departments = departments.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(departments, 10)  # Show 10 departments per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_departments = Department.objects.count()
    total_employees = Employee.objects.filter(status='Active').count()
    departments_with_employees = Department.objects.filter(employee__status='Active').distinct().count()

    context = {
        'departments': page_obj,
        'search_query': search_query,
        'total_departments': total_departments,
        'total_employees': total_employees,
        'departments_with_employees': departments_with_employees,
        'page_obj': page_obj,
    }

    return render(request, 'core/department_list.html', context)

@login_required
def department_create(request):
    """Create a new department"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            try:
                department = form.save()
                messages.success(request, f'Department "{department.name}" created successfully!')
                return redirect('core:department_list')
            except Exception as e:
                messages.error(request, f'Error creating department: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = DepartmentForm()

    context = {
        'form': form,
        'page_title': 'Add Department',
        'page_description': 'Create new department',
        'form_action': 'Create',
    }
    return render(request, 'core/department_form.html', context)

@login_required
def department_edit(request, pk):
    """Edit an existing department"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        department = get_object_or_404(Department, pk=pk)
    except Department.DoesNotExist:
        messages.error(request, 'Department not found.')
        return redirect('core:department_list')

    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            try:
                department = form.save()
                messages.success(request, f'Department "{department.name}" updated successfully!')
                return redirect('core:department_list')
            except Exception as e:
                messages.error(request, f'Error updating department: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = DepartmentForm(instance=department)

    context = {
        'form': form,
        'department': department,
        'page_title': 'Edit Department',
        'page_description': f'Update {department.name} information',
        'form_action': 'Update',
    }
    return render(request, 'core/department_form.html', context)

@login_required
def department_delete(request, pk):
    """Delete a department with proper validation"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        department = get_object_or_404(Department, pk=pk)
    except Department.DoesNotExist:
        messages.error(request, 'Department not found.')
        return redirect('core:department_list')

    # Check if department has active employees
    active_employees = department.employee_set.filter(status='Active').count()

    if request.method == 'POST':
        if active_employees > 0:
            messages.error(request,
                f'Cannot delete department "{department.name}" as it has {active_employees} active employee(s). '
                'Please reassign or deactivate employees first.')
            return redirect('core:department_list')

        try:
            department_name = department.name
            department.delete()
            messages.success(request, f'Department "{department_name}" deleted successfully!')
        except Exception as e:
            messages.error(request, f'Error deleting department: {str(e)}')

        return redirect('core:department_list')

    context = {
        'department': department,
        'active_employees': active_employees,
        'page_title': 'Delete Department',
        'page_description': f'Confirm deletion of {department.name}',
    }
    return render(request, 'core/department_delete.html', context)

@login_required
def department_detail(request, pk):
    """View department details with employee list and statistics"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        department = get_object_or_404(Department, pk=pk)
    except Department.DoesNotExist:
        messages.error(request, 'Department not found.')
        return redirect('core:department_list')

    # Get department employees with pagination
    employees = Employee.objects.filter(department=department).select_related('user', 'designation')

    # Filter by status if requested
    status_filter = request.GET.get('status', 'Active')
    if status_filter and status_filter != 'All':
        employees = employees.filter(status=status_filter)

    # Search employees
    search_query = request.GET.get('search', '')
    if search_query:
        employees = employees.filter(
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__email__icontains=search_query) |
            Q(employee_code__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(employees, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_employees = Employee.objects.filter(department=department).count()
    active_employees = Employee.objects.filter(department=department, status='Active').count()
    inactive_employees = Employee.objects.filter(department=department, status='Inactive').count()
    terminated_employees = Employee.objects.filter(department=department, status='Terminated').count()

    # Recent joiners (last 30 days)
    thirty_days_ago = date.today() - timedelta(days=30)
    recent_joiners = Employee.objects.filter(
        department=department,
        date_of_joining__gte=thirty_days_ago
    ).count()

    context = {
        'department': department,
        'employees': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'total_employees': total_employees,
        'active_employees': active_employees,
        'inactive_employees': inactive_employees,
        'terminated_employees': terminated_employees,
        'recent_joiners': recent_joiners,
        'page_obj': page_obj,
    }

    return render(request, 'core/department_detail.html', context)

@login_required
def designation_list(request):
    """List all designations with search and pagination"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    # Get search query
    search_query = request.GET.get('search', '')

    # Base queryset with employee count annotation
    designations = Designation.objects.annotate(
        employee_count=Count('employee', filter=Q(employee__status='Active'))
    ).order_by('title')

    # Apply search filter
    if search_query:
        designations = designations.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(designations, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_designations = Designation.objects.count()
    designations_with_employees = Designation.objects.filter(employee__status='Active').distinct().count()

    context = {
        'designations': page_obj,
        'search_query': search_query,
        'total_designations': total_designations,
        'designations_with_employees': designations_with_employees,
        'page_obj': page_obj,
    }

    return render(request, 'core/designation_list.html', context)

@login_required
def designation_create(request):
    """Create a new designation"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    if request.method == 'POST':
        form = DesignationForm(request.POST)
        if form.is_valid():
            try:
                designation = form.save()
                messages.success(request, f'Designation "{designation.title}" created successfully!')
                return redirect('core:designation_list')
            except Exception as e:
                messages.error(request, f'Error creating designation: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = DesignationForm()

    context = {
        'form': form,
        'page_title': 'Add Designation',
        'page_description': 'Create new job position',
        'form_action': 'Create',
    }
    return render(request, 'core/designation_form.html', context)

@login_required
def designation_edit(request, pk):
    """Edit an existing designation"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        designation = get_object_or_404(Designation, pk=pk)
    except Designation.DoesNotExist:
        messages.error(request, 'Designation not found.')
        return redirect('core:designation_list')

    if request.method == 'POST':
        form = DesignationForm(request.POST, instance=designation)
        if form.is_valid():
            try:
                designation = form.save()
                messages.success(request, f'Designation "{designation.title}" updated successfully!')
                return redirect('core:designation_list')
            except Exception as e:
                messages.error(request, f'Error updating designation: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = DesignationForm(instance=designation)

    context = {
        'form': form,
        'designation': designation,
        'page_title': 'Edit Designation',
        'page_description': f'Update {designation.title} information',
        'form_action': 'Update',
    }
    return render(request, 'core/designation_form.html', context)

@login_required
def designation_delete(request, pk):
    """Delete a designation with proper validation"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        designation = get_object_or_404(Designation, pk=pk)
    except Designation.DoesNotExist:
        messages.error(request, 'Designation not found.')
        return redirect('core:designation_list')

    # Check if designation has active employees
    active_employees = designation.employee_set.filter(status='Active').count()

    if request.method == 'POST':
        if active_employees > 0:
            messages.error(request,
                f'Cannot delete designation "{designation.title}" as it has {active_employees} active employee(s). '
                'Please reassign or deactivate employees first.')
            return redirect('core:designation_list')

        try:
            designation_title = designation.title
            designation.delete()
            messages.success(request, f'Designation "{designation_title}" deleted successfully!')
        except Exception as e:
            messages.error(request, f'Error deleting designation: {str(e)}')

        return redirect('core:designation_list')

    context = {
        'designation': designation,
        'active_employees': active_employees,
        'page_title': 'Delete Designation',
        'page_description': f'Confirm deletion of {designation.title}',
    }
    return render(request, 'core/designation_delete.html', context)

@login_required
def designation_detail(request, pk):
    """View designation details with employee list and statistics"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        designation = get_object_or_404(Designation, pk=pk)
    except Designation.DoesNotExist:
        messages.error(request, 'Designation not found.')
        return redirect('core:designation_list')

    # Get designation employees with pagination
    employees = Employee.objects.filter(designation=designation).select_related('user', 'department')

    # Filter by status if requested
    status_filter = request.GET.get('status', 'Active')
    if status_filter and status_filter != 'All':
        employees = employees.filter(status=status_filter)

    # Search employees
    search_query = request.GET.get('search', '')
    if search_query:
        employees = employees.filter(
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__email__icontains=search_query) |
            Q(employee_code__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(employees, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_employees = Employee.objects.filter(designation=designation).count()
    active_employees = Employee.objects.filter(designation=designation, status='Active').count()
    inactive_employees = Employee.objects.filter(designation=designation, status='Inactive').count()
    terminated_employees = Employee.objects.filter(designation=designation, status='Terminated').count()

    context = {
        'designation': designation,
        'employees': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'total_employees': total_employees,
        'active_employees': active_employees,
        'inactive_employees': inactive_employees,
        'terminated_employees': terminated_employees,
        'page_obj': page_obj,
    }

    return render(request, 'core/designation_detail.html', context)

@login_required
def leave_type_list(request):
    """List all leave types with search and pagination"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    # Get search query
    search_query = request.GET.get('search', '')

    # Base queryset
    leave_types = LeaveType.objects.all().order_by('name')

    # Apply search filter
    if search_query:
        leave_types = leave_types.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(leave_types, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_leave_types = LeaveType.objects.count()
    active_leave_types = LeaveType.objects.filter(is_active=True).count()
    inactive_leave_types = LeaveType.objects.filter(is_active=False).count()

    context = {
        'leave_types': page_obj,
        'search_query': search_query,
        'total_leave_types': total_leave_types,
        'active_leave_types': active_leave_types,
        'inactive_leave_types': inactive_leave_types,
        'page_obj': page_obj,
    }

    return render(request, 'core/leave_type_list.html', context)

@login_required
def leave_type_create(request):
    """Create a new leave type"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    if request.method == 'POST':
        form = LeaveTypeForm(request.POST)
        if form.is_valid():
            try:
                leave_type = form.save()
                messages.success(request, f'Leave type "{leave_type.name}" created successfully!')
                return redirect('core:leave_type_list')
            except Exception as e:
                messages.error(request, f'Error creating leave type: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = LeaveTypeForm()

    context = {
        'form': form,
        'page_title': 'Add Leave Type',
        'page_description': 'Create new leave type',
        'form_action': 'Create',
    }
    return render(request, 'core/leave_type_form.html', context)

@login_required
def leave_type_edit(request, pk):
    """Edit an existing leave type"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        leave_type = get_object_or_404(LeaveType, pk=pk)
    except LeaveType.DoesNotExist:
        messages.error(request, 'Leave type not found.')
        return redirect('core:leave_type_list')

    if request.method == 'POST':
        form = LeaveTypeForm(request.POST, instance=leave_type)
        if form.is_valid():
            try:
                leave_type = form.save()
                messages.success(request, f'Leave type "{leave_type.name}" updated successfully!')
                return redirect('core:leave_type_list')
            except Exception as e:
                messages.error(request, f'Error updating leave type: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = LeaveTypeForm(instance=leave_type)

    context = {
        'form': form,
        'leave_type': leave_type,
        'page_title': 'Edit Leave Type',
        'page_description': f'Update {leave_type.name} information',
        'form_action': 'Update',
    }
    return render(request, 'core/leave_type_form.html', context)

@login_required
def leave_type_delete(request, pk):
    """Delete a leave type with proper validation"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        leave_type = get_object_or_404(LeaveType, pk=pk)
    except LeaveType.DoesNotExist:
        messages.error(request, 'Leave type not found.')
        return redirect('core:leave_type_list')

    # Check if leave type is being used
    leave_requests_count = leave_type.leaverequest_set.count()
    leave_balances_count = leave_type.leavebalance_set.count()

    if request.method == 'POST':
        if leave_requests_count > 0 or leave_balances_count > 0:
            messages.error(request,
                f'Cannot delete leave type "{leave_type.name}" as it is being used in '
                f'{leave_requests_count} leave request(s) and {leave_balances_count} leave balance(s).')
            return redirect('core:leave_type_list')

        try:
            leave_type_name = leave_type.name
            leave_type.delete()
            messages.success(request, f'Leave type "{leave_type_name}" deleted successfully!')
        except Exception as e:
            messages.error(request, f'Error deleting leave type: {str(e)}')

        return redirect('core:leave_type_list')

    context = {
        'leave_type': leave_type,
        'leave_requests_count': leave_requests_count,
        'leave_balances_count': leave_balances_count,
        'page_title': 'Delete Leave Type',
        'page_description': f'Confirm deletion of {leave_type.name}',
    }
    return render(request, 'core/leave_type_delete.html', context)

@login_required
def leave_type_detail(request, pk):
    """View leave type details with usage statistics"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    try:
        leave_type = get_object_or_404(LeaveType, pk=pk)
    except LeaveType.DoesNotExist:
        messages.error(request, 'Leave type not found.')
        return redirect('core:leave_type_list')

    # Get usage statistics
    total_leave_requests = leave_type.leaverequest_set.count()
    pending_requests = leave_type.leaverequest_set.filter(status='Pending').count()
    approved_requests = leave_type.leaverequest_set.filter(status='Approved').count()
    rejected_requests = leave_type.leaverequest_set.filter(status='Rejected').count()

    # Get leave balances
    total_balances = leave_type.leavebalance_set.count()
    current_year = date.today().year
    current_year_balances = leave_type.leavebalance_set.filter(year=current_year)

    # Recent leave requests
    recent_requests = leave_type.leaverequest_set.select_related(
        'employee__user'
    ).order_by('-applied_on')[:10]

    context = {
        'leave_type': leave_type,
        'total_leave_requests': total_leave_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'rejected_requests': rejected_requests,
        'total_balances': total_balances,
        'current_year_balances': current_year_balances,
        'recent_requests': recent_requests,
        'current_year': current_year,
    }

    return render(request, 'core/leave_type_detail.html', context)

@login_required
def holiday_list(request):
    """List all holidays with filtering and pagination"""
    # Get current year or year from query parameter
    current_year = date.today().year
    year = request.GET.get('year', current_year)
    try:
        year = int(year)
    except (ValueError, TypeError):
        year = current_year

    # Get holidays for the selected year
    holidays = HolidayCalendar.objects.filter(date__year=year).order_by('date')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        holidays = holidays.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by optional/mandatory
    holiday_type = request.GET.get('type', '')
    if holiday_type == 'optional':
        holidays = holidays.filter(is_optional=True)
    elif holiday_type == 'mandatory':
        holidays = holidays.filter(is_optional=False)

    # Pagination
    paginator = Paginator(holidays, 12)  # 12 holidays per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get available years for filter
    available_years = HolidayCalendar.objects.dates('date', 'year').values_list('date__year', flat=True)
    available_years = sorted(set(available_years), reverse=True)

    # Get upcoming holidays for quick view
    upcoming_holidays = HolidayCalendar.objects.filter(
        date__gte=date.today()
    ).order_by('date')[:5]

    context = {
        'page_obj': page_obj,
        'current_year': year,
        'available_years': available_years,
        'search_query': search_query,
        'holiday_type': holiday_type,
        'upcoming_holidays': upcoming_holidays,
        'is_admin': request.user.role == 'admin',
        'total_holidays': holidays.count(),
    }
    return render(request, 'core/holiday_list.html', context)

@login_required
def holiday_create(request):
    """Create a new holiday (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to add holidays.')
        return redirect('core:holiday_list')

    if request.method == 'POST':
        from .forms import HolidayForm
        form = HolidayForm(request.POST)
        if form.is_valid():
            try:
                holiday = form.save()
                messages.success(request, f'Holiday "{holiday.name}" added successfully!')
                return redirect('core:holiday_list')
            except Exception as e:
                messages.error(request, f'Error adding holiday: {str(e)}')
    else:
        from .forms import HolidayForm
        form = HolidayForm()
        # Set minimum date to today
        form.fields['date'].widget.attrs['min'] = date.today().isoformat()

    context = {
        'form': form,
        'is_edit': False,
    }
    return render(request, 'core/holiday_form.html', context)

@login_required
def holiday_edit(request, pk):
    """Edit an existing holiday (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to edit holidays.')
        return redirect('core:holiday_list')

    try:
        holiday = get_object_or_404(HolidayCalendar, pk=pk)

        if request.method == 'POST':
            from .forms import HolidayForm
            form = HolidayForm(request.POST, instance=holiday)
            if form.is_valid():
                try:
                    updated_holiday = form.save()
                    messages.success(request, f'Holiday "{updated_holiday.name}" updated successfully!')
                    return redirect('core:holiday_list')
                except Exception as e:
                    messages.error(request, f'Error updating holiday: {str(e)}')
        else:
            from .forms import HolidayForm
            form = HolidayForm(instance=holiday)

        context = {
            'form': form,
            'holiday': holiday,
            'is_edit': True,
        }
        return render(request, 'core/holiday_form.html', context)

    except Exception as e:
        messages.error(request, f'Error loading holiday: {str(e)}')
        return redirect('core:holiday_list')

@login_required
def holiday_delete(request, pk):
    """Delete a holiday (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to delete holidays.')
        return redirect('core:holiday_list')

    try:
        holiday = get_object_or_404(HolidayCalendar, pk=pk)
        holiday_name = holiday.name
        holiday.delete()
        messages.success(request, f'Holiday "{holiday_name}" deleted successfully!')
    except Exception as e:
        messages.error(request, f'Error deleting holiday: {str(e)}')

    return redirect('core:holiday_list')

@login_required
def leave_request_detail(request, pk):
    """View detailed information about a leave request"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request
        if request.user.role == 'admin':
            # Admin can view any leave request
            leave_request = get_object_or_404(LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user'), pk=pk)
        else:
            # Regular users can only view their own leave requests
            leave_request = get_object_or_404(LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user'), pk=pk, employee=employee)

        context = {
            'leave_request': leave_request,
            'is_admin': request.user.role == 'admin',
            'can_edit': leave_request.status == 'Pending' and leave_request.employee == employee,
            'can_cancel': leave_request.status == 'Pending' and leave_request.employee == employee,
            'can_approve': request.user.role == 'admin' and leave_request.status == 'Pending',
        }
        return render(request, 'core/leave_request_detail.html', context)

    except Exception as e:
        messages.error(request, f'Error loading leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_request_edit(request, pk):
    """Edit a leave request (only if pending and belongs to current user)"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request - only allow editing own requests
        leave_request = get_object_or_404(LeaveRequest.objects.select_related('leave_type'), pk=pk, employee=employee)

        # Only allow editing pending requests
        if leave_request.status != 'Pending':
            messages.error(request, 'You can only edit pending leave requests.')
            return redirect('core:leave_request_detail', pk=pk)

        if request.method == 'POST':
            from .forms import LeaveRequestForm
            form = LeaveRequestForm(request.POST, instance=leave_request, employee=employee)
            if form.is_valid():
                updated_request = form.save(commit=False)
                updated_request.employee = employee
                updated_request.save()
                messages.success(request, 'Leave request updated successfully!')
                return redirect('core:leave_request_detail', pk=pk)
        else:
            from .forms import LeaveRequestForm
            form = LeaveRequestForm(instance=leave_request, employee=employee)

        context = {
            'form': form,
            'leave_request': leave_request,
            'is_edit': True,
        }
        return render(request, 'core/leave_request_edit.html', context)

    except Exception as e:
        messages.error(request, f'Error loading leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_request_cancel(request, pk):
    """Cancel a leave request (only if pending and belongs to current user)"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request - only allow cancelling own requests
        leave_request = get_object_or_404(LeaveRequest, pk=pk, employee=employee)

        # Only allow cancelling pending requests
        if leave_request.status != 'Pending':
            messages.error(request, 'You can only cancel pending leave requests.')
            return redirect('core:leave_request_detail', pk=pk)

        # Update status to cancelled
        leave_request.status = 'Cancelled'
        leave_request.save()

        messages.success(request, 'Leave request cancelled successfully!')
        return redirect('core:leave_request_list')

    except Exception as e:
        messages.error(request, f'Error cancelling leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_request_approve(request, pk):
    """Approve or reject a leave request (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to approve leave requests.')
        return redirect('core:leave_request_list')

    try:
        admin_employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request
        leave_request = get_object_or_404(
            LeaveRequest.objects.select_related('employee__user', 'leave_type'),
            pk=pk
        )

        # Only allow approving pending requests
        if leave_request.status != 'Pending':
            messages.error(request, 'This leave request has already been processed.')
            return redirect('core:leave_request_detail', pk=pk)

        if request.method == 'POST':
            from .forms import LeaveApprovalForm
            form = LeaveApprovalForm(request.POST, instance=leave_request)
            if form.is_valid():
                try:
                    # Save the approval decision
                    approved_request = form.save(commit=False)
                    approved_request.approved_by = admin_employee
                    approved_request.approved_on = timezone.now()

                    # If approved, update leave balance
                    if approved_request.status == 'Approved':
                        update_leave_balance(approved_request)
                        messages.success(request, f'Leave request approved successfully for {approved_request.employee.full_name}!')
                    else:
                        messages.success(request, f'Leave request rejected for {approved_request.employee.full_name}.')

                    approved_request.save()

                    # Send email notification to employee
                    send_leave_notification(approved_request)

                    return redirect('core:leave_request_detail', pk=pk)

                except Exception as e:
                    messages.error(request, f'Error processing leave request: {str(e)}')
        else:
            from .forms import LeaveApprovalForm
            form = LeaveApprovalForm(instance=leave_request)

        # Check leave balance availability
        balance_info = check_leave_balance_availability(leave_request)

        context = {
            'form': form,
            'leave_request': leave_request,
            'employee': leave_request.employee,
            'balance_info': balance_info,
        }
        return render(request, 'core/leave_approval.html', context)

    except Exception as e:
        messages.error(request, f'Error loading leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_balance_list(request):
    context = {'page_title': 'Leave Balance Management', 'page_description': 'Manage employee leave balances'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_balance_create(request):
    context = {'page_title': 'Add Leave Balance', 'page_description': 'Create leave balance for employee'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_balance_edit(request, pk):
    context = {'page_title': 'Edit Leave Balance', 'page_description': 'Update employee leave balance'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_list(request):
    """List attendance records for the current user or all if admin"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.user.role == 'admin':
        attendance_records = Attendance.objects.select_related('employee__user').order_by('-date')
    else:
        attendance_records = Attendance.objects.filter(employee=employee).order_by('-date')

    # Get filter parameters
    month = request.GET.get('month')
    year = request.GET.get('year')
    status = request.GET.get('status')

    # Apply filters
    if month:
        attendance_records = attendance_records.filter(date__month=month)
    if year:
        attendance_records = attendance_records.filter(date__year=year)
    if status:
        attendance_records = attendance_records.filter(status=status)

    # Pagination
    paginator = Paginator(attendance_records, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate monthly stats
    current_month = date.today().month
    current_year = date.today().year

    monthly_records = Attendance.objects.filter(
        employee=employee,
        date__month=current_month,
        date__year=current_year
    )

    present_days = monthly_records.filter(status='Present').count()
    absent_days = monthly_records.filter(status='Absent').count()
    leave_days = monthly_records.filter(status='Leave').count()
    wfh_days = monthly_records.filter(status='WFH').count()
    total_hours = monthly_records.aggregate(total=Sum('working_hours'))['total'] or 0

    context = {
        'page_obj': page_obj,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'wfh_days': wfh_days,
        'total_hours': total_hours,
        'is_admin': request.user.role == 'admin',
        'current_month': date.today().strftime('%B %Y'),
    }

    return render(request, 'core/attendance_list.html', context)

@login_required
def mark_attendance(request):
    """Mark attendance for the current user"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.method == 'POST':
        from .forms import AttendanceForm
        form = AttendanceForm(request.POST)
        if form.is_valid():
            attendance = form.save(commit=False)
            attendance.employee = employee

            # Get client IP address
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                attendance.ip_address = x_forwarded_for.split(',')[0]
            else:
                attendance.ip_address = request.META.get('REMOTE_ADDR')

            attendance.save()
            messages.success(request, 'Attendance marked successfully!')
            return redirect('core:attendance_list')
    else:
        from .forms import AttendanceForm
        # Check if attendance already exists for today
        today = date.today()
        existing_attendance = Attendance.objects.filter(
            employee=employee,
            date=today
        ).first()

        if existing_attendance:
            form = AttendanceForm(instance=existing_attendance)
            messages.info(request, 'Attendance already marked for today. You can update it below.')
        else:
            form = AttendanceForm(initial={'date': today})

    context = {
        'form': form,
        'today': date.today().isoformat(),
    }

    return render(request, 'core/attendance_form.html', context)

@login_required
def attendance_edit(request, pk):
    context = {'page_title': 'Edit Attendance', 'page_description': 'Update attendance record'}
    return render(request, 'core/placeholder_template.html', context)

# Reports Views
@login_required
def reports_dashboard(request):
    """Reports dashboard for Leave & Attendance Management"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    context = {
        'page_title': 'Reports Dashboard',
        'page_description': 'Generate and view various reports'
    }
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_summary_report(request):
    """Attendance summary report"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('core:dashboard')

    context = {
        'page_title': 'Attendance Summary Report',
        'page_description': 'View attendance summary and statistics'
    }
    return render(request, 'core/placeholder_template.html', context)



@login_required
def get_holidays(request):
    """API endpoint to get holidays for a date range"""
    try:
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        year = request.GET.get('year')

        if year:
            holidays = HolidayCalendar.objects.filter(date__year=year)
        elif start_date and end_date:
            holidays = HolidayCalendar.objects.filter(
                date__range=[start_date, end_date]
            )
        else:
            # Default to current year
            holidays = HolidayCalendar.objects.filter(date__year=date.today().year)

        holidays_data = []
        for holiday in holidays:
            holidays_data.append({
                'date': holiday.date.isoformat(),
                'name': holiday.name,
                'description': holiday.description or '',
                'is_optional': holiday.is_optional,
                'formatted_date': holiday.date.strftime('%B %d, %Y'),
                'day_name': holiday.date.strftime('%A'),
            })

        return JsonResponse({
            'success': True,
            'holidays': holidays_data,
            'count': len(holidays_data)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)

