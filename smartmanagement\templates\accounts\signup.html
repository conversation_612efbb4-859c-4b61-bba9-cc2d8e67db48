{% extends 'base.html' %}

{% block title %}Sign Up - Smart Management System{% endblock %}

{% block content %}
<section class="py-3">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card animate-on-scroll">
                    <div class="card-body p-3">
                        <div class="text-center mb-3">
                            <i class="fas fa-user-plus fa-2x text-primary mb-2"></i>
                            <h4 class="card-title mb-1">Create Account</h4>
                            <p class="text-muted small mb-0">Join us and start managing smartly</p>
                        </div>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <!-- Error Summary -->
                        <div id="errorSummary" class="error-summary" style="display: none;">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul id="errorList"></ul>
                        </div>

                        <form method="post" id="signupForm">
                            {% csrf_token %}

                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label small required-field">
                                        <i class="fas fa-user me-1"></i>First Name
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-sm"
                                           id="{{ form.first_name.id_for_label }}"
                                           name="{{ form.first_name.name }}"
                                           placeholder="First name"
                                           required>
                                    {% if form.first_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.first_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-2">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label small required-field">
                                        <i class="fas fa-user me-1"></i>Last Name
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-sm"
                                           id="{{ form.last_name.id_for_label }}"
                                           name="{{ form.last_name.name }}"
                                           placeholder="Last name"
                                           required>
                                    {% if form.last_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.last_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <label for="{{ form.username.id_for_label }}" class="form-label small required-field">
                                        <i class="fas fa-at me-1"></i>Username
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-sm"
                                           id="{{ form.username.id_for_label }}"
                                           name="{{ form.username.name }}"
                                           placeholder="Username"
                                           required>
                                    {% if form.username.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.username.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-2">
                                    <label for="{{ form.email.id_for_label }}" class="form-label small required-field">
                                        <i class="fas fa-envelope me-1"></i>Email Address
                                    </label>
                                    <input type="email"
                                           class="form-control form-control-sm"
                                           id="{{ form.email.id_for_label }}"
                                           name="{{ form.email.name }}"
                                           placeholder="Email address"
                                           required>
                                    {% if form.email.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8 mb-2">
                                    <label for="{{ form.password.id_for_label }}" class="form-label small required-field">
                                        <i class="fas fa-lock me-1"></i>Password
                                    </label>
                                    <div class="input-group input-group-sm">
                                        <input type="password"
                                               class="form-control"
                                               id="{{ form.password.id_for_label }}"
                                               name="{{ form.password.name }}"
                                               placeholder="Password"
                                               required>
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="togglePassword('{{ form.password.id_for_label }}', 'toggleIcon1')">
                                            <i class="fas fa-eye" id="toggleIcon1"></i>
                                        </button>
                                    </div>
                                    {% if form.password.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-2">
                                    <label for="{{ form.role.id_for_label }}" class="form-label small required-field">
                                        <i class="fas fa-user-tag me-1"></i>Role
                                    </label>
                                    <select class="form-control form-control-sm"
                                            id="{{ form.role.id_for_label }}"
                                            name="{{ form.role.name }}"
                                            required>
                                        <option value="">Select role</option>
                                        <option value="admin">Admin</option>
                                        <option value="employee">Employee</option>
                                    </select>
                                    {% if form.role.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.role.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-2 form-check">
                                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                                <label class="form-check-label small required-field" for="agreeTerms">
                                    I agree to the <a href="#" class="text-primary">Terms and Conditions</a>
                                </label>
                            </div>

                            <div class="d-grid mb-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0 small">Already have an account?
                                    <a href="{% url 'login' %}" class="text-primary text-decoration-none">
                                        <strong>Sign in here</strong>
                                    </a>
                                </p>
                            </div>
                        </form>

                        <div class="loading">
                            <div class="spinner"></div>
                            <p class="mt-2 small">Creating your account...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword(fieldId, iconId) {
        const passwordField = document.getElementById(fieldId);
        const toggleIcon = document.getElementById(iconId);

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    document.getElementById('signupForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
        showLoading('signupForm');
    });

    // Form validation function
    function validateForm() {
        const errors = [];
        const form = document.getElementById('signupForm');
        const errorSummary = document.getElementById('errorSummary');
        const errorList = document.getElementById('errorList');

        // Clear previous errors
        errorList.innerHTML = '';
        errorSummary.style.display = 'none';

        // Remove previous validation classes
        form.querySelectorAll('.form-control').forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });

        // Validate required fields
        const requiredFields = [
            { id: '{{ form.first_name.id_for_label }}', name: 'First Name' },
            { id: '{{ form.last_name.id_for_label }}', name: 'Last Name' },
            { id: '{{ form.username.id_for_label }}', name: 'Username' },
            { id: '{{ form.email.id_for_label }}', name: 'Email Address' },
            { id: '{{ form.password.id_for_label }}', name: 'Password' },
            { id: '{{ form.role.id_for_label }}', name: 'Role' }
        ];

        requiredFields.forEach(field => {
            const input = document.getElementById(field.id);
            if (!input.value.trim()) {
                errors.push(`${field.name} is required`);
                input.classList.add('is-invalid');
            } else {
                input.classList.add('is-valid');
            }
        });

        // Validate email format
        const emailInput = document.getElementById('{{ form.email.id_for_label }}');
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailInput.value && !emailPattern.test(emailInput.value)) {
            errors.push('Please enter a valid email address');
            emailInput.classList.remove('is-valid');
            emailInput.classList.add('is-invalid');
        }

        // Validate password strength
        const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
        if (passwordInput.value && passwordInput.value.length < 8) {
            errors.push('Password must be at least 8 characters long');
            passwordInput.classList.remove('is-valid');
            passwordInput.classList.add('is-invalid');
        }

        // Validate terms agreement
        const termsCheckbox = document.getElementById('agreeTerms');
        if (!termsCheckbox.checked) {
            errors.push('You must agree to the Terms and Conditions');
        }

        // Show errors if any
        if (errors.length > 0) {
            errors.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error;
                errorList.appendChild(li);
            });
            errorSummary.style.display = 'block';
            errorSummary.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return false;
        }

        return true;
    }

    // Real-time validation
    document.addEventListener('DOMContentLoaded', function() {
        const requiredInputs = document.querySelectorAll('input[required], select[required]');

        requiredInputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });

        // Email validation
        const emailInput = document.getElementById('{{ form.email.id_for_label }}');
        emailInput.addEventListener('blur', function() {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (this.value && !emailPattern.test(this.value)) {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Password strength indicator
    document.getElementById('{{ form.password.id_for_label }}').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('strengthText');

        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];

        if (strengthBar && strengthText) {
            strengthBar.style.width = (strength * 20) + '%';
            strengthBar.style.backgroundColor = strengthColors[strength - 1] || '#e5e7eb';
            strengthText.textContent = strengthLevels[strength - 1] || '';
            strengthText.style.color = strengthColors[strength - 1] || '#6b7280';
        }
    });
</script>
{% endblock %}
