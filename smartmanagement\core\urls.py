from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    # Dashboard URLs
    path('', views.leave_attendance_dashboard, name='dashboard'),
    path('admin-dashboard/', views.admin_leave_dashboard, name='admin_dashboard'),
    
    # Employee Management URLs
    path('employees/', views.employee_list, name='employee_list'),
    path('employees/create/', views.employee_create, name='employee_create'),
    path('employees/<int:pk>/', views.employee_detail, name='employee_detail'),
    path('employees/<int:pk>/edit/', views.employee_edit, name='employee_edit'),
    path('employees/<int:pk>/delete/', views.employee_delete, name='employee_delete'),
    path('create-profile/', views.create_employee_profile, name='create_employee_profile'),
    path('edit-profile/', views.edit_employee_profile, name='edit_employee_profile'),
    
    # Department Management URLs
    path('departments/', views.department_list, name='department_list'),
    path('departments/create/', views.department_create, name='department_create'),
    path('departments/<int:pk>/', views.department_detail, name='department_detail'),
    path('departments/<int:pk>/edit/', views.department_edit, name='department_edit'),
    path('departments/<int:pk>/delete/', views.department_delete, name='department_delete'),
    
    # Designation Management URLs
    path('designations/', views.designation_list, name='designation_list'),
    path('designations/create/', views.designation_create, name='designation_create'),
    path('designations/<int:pk>/', views.designation_detail, name='designation_detail'),
    path('designations/<int:pk>/edit/', views.designation_edit, name='designation_edit'),
    path('designations/<int:pk>/delete/', views.designation_delete, name='designation_delete'),
    
    # Leave Type Management URLs
    path('leave-types/', views.leave_type_list, name='leave_type_list'),
    path('leave-types/create/', views.leave_type_create, name='leave_type_create'),
    path('leave-types/<int:pk>/', views.leave_type_detail, name='leave_type_detail'),
    path('leave-types/<int:pk>/edit/', views.leave_type_edit, name='leave_type_edit'),
    path('leave-types/<int:pk>/delete/', views.leave_type_delete, name='leave_type_delete'),
    
    # Holiday Management URLs
    path('holidays/', views.holiday_list, name='holiday_list'),
    path('holidays/create/', views.holiday_create, name='holiday_create'),
    path('holidays/<int:pk>/edit/', views.holiday_edit, name='holiday_edit'),
    path('holidays/<int:pk>/delete/', views.holiday_delete, name='holiday_delete'),
    
    # Leave Request URLs
    path('leave-requests/', views.leave_request_list, name='leave_request_list'),
    path('leave-requests/create/', views.leave_request_create, name='leave_request_create'),
    path('leave-requests/<int:pk>/', views.leave_request_detail, name='leave_request_detail'),
    path('leave-requests/<int:pk>/edit/', views.leave_request_edit, name='leave_request_edit'),
    path('leave-requests/<int:pk>/cancel/', views.leave_request_cancel, name='leave_request_cancel'),
    path('leave-requests/<int:pk>/approve/', views.leave_request_approve, name='leave_request_approve'),
    
    # Leave Balance URLs
    path('leave-balances/', views.leave_balance_list, name='leave_balance_list'),
    path('leave-balances/create/', views.leave_balance_create, name='leave_balance_create'),
    path('leave-balances/<int:pk>/edit/', views.leave_balance_edit, name='leave_balance_edit'),
    
    # Attendance URLs
    path('attendance/', views.attendance_list, name='attendance_list'),
    path('attendance/mark/', views.mark_attendance, name='mark_attendance'),
    path('attendance/<int:pk>/edit/', views.attendance_edit, name='attendance_edit'),

    # Reports URLs
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/attendance-summary/', views.attendance_summary_report, name='attendance_summary_report'),

    # API URLs for AJAX calls
    path('api/check-leave-balance/', views.check_leave_balance, name='check_leave_balance'),
    path('api/calculate-leave-days/', views.calculate_leave_days, name='calculate_leave_days'),
    path('api/get-holidays/', views.get_holidays, name='get_holidays'),
]
